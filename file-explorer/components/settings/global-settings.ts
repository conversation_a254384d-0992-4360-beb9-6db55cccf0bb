// components/settings/global-settings.ts
import { SettingsManager } from './settings-manager';

/**
 * ✅ Global SettingsManager Instance
 * Ensures single source of truth for settings across the app
 */
let globalSettingsManager: SettingsManager | null = null;

export const getGlobalSettingsManager = (): SettingsManager => {
  if (!globalSettingsManager) {
    globalSettingsManager = new SettingsManager();
  }
  return globalSettingsManager;
};
