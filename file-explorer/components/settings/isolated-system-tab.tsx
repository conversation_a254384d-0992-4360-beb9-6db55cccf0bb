// components/settings/isolated-system-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { SettingsManager, SystemSettings } from './settings-manager';

interface IsolatedSystemTabProps {
  settings: SystemSettings;
  updateSystemSettings: (updates: Partial<SystemSettings>) => void;
}

/**
 * ✅ Isolated System Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Agents tab
 */
export const IsolatedSystemTab = React.memo<IsolatedSystemTabProps>(({
  settings,
  updateSystemSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<SystemSettings>(settings);

  useEffect(() => {
    console.log('🔄 IsolatedSystemTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof SystemSettings) => {
    console.time('system-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updateSystemSettings({ [key]: newValue });
    console.timeEnd('system-toggle-latency');
  }, [localSettings, updateSystemSettings]);

  // ✅ Individual slider handlers (matching Agents tab pattern)
  const handleAutoSaveIntervalChange = useCallback((value: number) => {
    console.time('system-slider-latency');
    setLocalSettings(prev => ({ ...prev, autoSaveInterval: value }));
    console.timeEnd('system-slider-latency');
  }, []);

  const handleMaxTasksChange = useCallback((value: number) => {
    console.time('system-slider-latency');
    setLocalSettings(prev => ({ ...prev, maxConcurrentTasks: value }));
    console.timeEnd('system-slider-latency');
  }, []);

  // ✅ Commit handlers (matching Agents tab pattern)
  const commitAutoSaveInterval = useCallback(() => {
    console.time('system-slider-commit');
    updateSystemSettings({ autoSaveInterval: localSettings.autoSaveInterval });
    console.timeEnd('system-slider-commit');
  }, [localSettings.autoSaveInterval, updateSystemSettings]);

  const commitMaxTasks = useCallback(() => {
    console.time('system-slider-commit');
    updateSystemSettings({ maxConcurrentTasks: localSettings.maxConcurrentTasks });
    console.timeEnd('system-slider-commit');
  }, [localSettings.maxConcurrentTasks, updateSystemSettings]);

  // ✅ Memoized toggle component
  const SystemToggle = React.memo(({
    id,
    label,
    settingKey
  }: {
    id: string;
    label: string;
    settingKey: keyof SystemSettings;
  }) => (
    <div className="flex items-center justify-between">
      <Label htmlFor={id}>{label}</Label>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider components (matching Agents tab pattern)
  const AutoSaveIntervalSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Auto Save Interval (seconds)</Label>
      <Slider
        value={[localSettings.autoSaveInterval]}
        onValueChange={([value]) => handleAutoSaveIntervalChange(value)}
        onPointerUp={commitAutoSaveInterval}
        min={10}
        max={300}
        step={10}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.autoSaveInterval} seconds
      </div>
    </div>
  ));

  const MaxTasksSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Max Concurrent Tasks</Label>
      <Slider
        value={[localSettings.maxConcurrentTasks]}
        onValueChange={([value]) => handleMaxTasksChange(value)}
        onPointerUp={commitMaxTasks}
        min={1}
        max={20}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.maxConcurrentTasks} tasks
      </div>
    </div>
  ));

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>System Settings</CardTitle>
          <CardDescription>Configure global application behavior</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SystemToggle
            id="auto-save"
            label="Auto Save"
            settingKey="autoSave"
          />

          <AutoSaveIntervalSlider />

          <MaxTasksSlider />

          <SystemToggle
            id="debug-mode"
            label="Debug Mode"
            settingKey="debugMode"
          />
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedSystemTab.displayName = 'IsolatedSystemTab';
