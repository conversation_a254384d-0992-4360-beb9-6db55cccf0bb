"use client"

import React, { ReactNode, useEffect, useState } from 'react';
import { SettingsProvider } from './settings-context';
import { ThemeBridge } from './theme-bridge';
import { getGlobalSettingsManager } from './global-settings';
import { SettingsManager } from './settings-manager';
import { AutoSaveProvider } from '../auto-save/auto-save-provider';

interface ClientSettingsWrapperProps {
  children: ReactNode;
}

/**
 * ✅ Client-side Settings Wrapper
 * Handles SettingsManager instantiation on the client side
 * Provides SettingsProvider and ThemeBridge to the app
 */
export const ClientSettingsWrapper: React.FC<ClientSettingsWrapperProps> = ({ children }) => {
  const [settingsManager, setSettingsManager] = useState<SettingsManager | null>(null);

  useEffect(() => {
    // Initialize SettingsManager on the client side only
    const manager = getGlobalSettingsManager();
    setSettingsManager(manager);
  }, []);

  // Don't render until SettingsManager is initialized
  if (!settingsManager) {
    return <>{children}</>;
  }

  return (
    <SettingsProvider settingsManager={settingsManager}>
      <ThemeBridge />
      <AutoSaveProvider>
        {children}
      </AutoSaveProvider>
    </SettingsProvider>
  );
};
