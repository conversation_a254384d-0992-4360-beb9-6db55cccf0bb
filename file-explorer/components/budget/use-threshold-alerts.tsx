// components/budget/use-threshold-alerts.tsx
"use client"

import { useState, useEffect, useCallback } from 'react';
import { alertManager, ThresholdAlert } from '../../lib/alert-manager';
import { notificationService, showAlertNotification } from '../../lib/notification-service';
import { useSystemSettings } from '../settings/settings-context';
import { useToast } from '@/components/ui/use-toast';

/**
 * ✅ React Hook for Threshold Alerts
 * Integrates alert system with React components and toast notifications
 */
export function useThresholdAlerts() {
  const settingsContext = useSystemSettings();
  const { toast } = useToast();
  const [alerts, setAlerts] = useState<ThresholdAlert[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // ✅ Defensive check for undefined costSettings
  const costSettings = settingsContext?.costSettings || {
    trackUsage: false,
    budgetLimit: 100,
    alertThreshold: 80,
    showCostEstimates: false,
    preferCheaperModels: false
  };

  // Initialize notification service with toast function
  useEffect(() => {
    if (!isInitialized) {
      notificationService.initialize(toast);
      setIsInitialized(true);
    }
  }, [toast, isInitialized]);

  // Subscribe to alert notifications
  useEffect(() => {
    const unsubscribe = alertManager.onAlert((alert: ThresholdAlert) => {
      // Update alerts state
      setAlerts(prev => [alert, ...prev.slice(0, 9)]); // Keep last 10 alerts

      // Show toast notification
      showAlertNotification(alert, {
        duration: alert.type === 'budget_exceeded' ? 15000 : 8000,
        showAction: true,
        actionText: 'View Settings',
        onAction: () => {
          // This would typically open settings
          console.log('Open cost settings');
        }
      });

      // Show Electron notification if available
      if (typeof window !== 'undefined' && window.electronAPI?.notification) {
        notificationService.showElectronNotification(alert);
      }
    });

    // Load existing alert history
    const history = alertManager.getAlertHistory();
    setAlerts(history.slice(0, 10)); // Show last 10 alerts

    return unsubscribe;
  }, []);

  // Manual threshold check
  const checkThresholds = useCallback(() => {
    if (!costSettings.trackUsage) {
      return null;
    }

    return alertManager.checkThresholds(costSettings);
  }, [costSettings]);

  // Force threshold check (for testing)
  const forceCheckThresholds = useCallback(() => {
    if (!costSettings.trackUsage) {
      return null;
    }

    return alertManager.forceCheckThresholds(costSettings);
  }, [costSettings]);

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    alertManager.acknowledgeAlert(alertId);
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    );
  }, []);

  // Clear alert history
  const clearAlerts = useCallback(() => {
    alertManager.clearAlertHistory();
    setAlerts([]);
  }, []);

  // Reset alerts for testing
  const resetAlertsForTesting = useCallback(() => {
    alertManager.resetAlertsForTesting();
    setAlerts([]);
  }, []);

  // Get alert statistics
  const getAlertStats = useCallback(() => {
    const total = alerts.length;
    const acknowledged = alerts.filter(a => a.acknowledged).length;
    const unacknowledged = total - acknowledged;
    const thresholdAlerts = alerts.filter(a => a.type === 'threshold_exceeded').length;
    const budgetAlerts = alerts.filter(a => a.type === 'budget_exceeded').length;

    return {
      total,
      acknowledged,
      unacknowledged,
      thresholdAlerts,
      budgetAlerts
    };
  }, [alerts]);

  return {
    alerts,
    checkThresholds,
    forceCheckThresholds,
    acknowledgeAlert,
    clearAlerts,
    resetAlertsForTesting,
    getAlertStats,
    isInitialized
  };
}

/**
 * ✅ Simple hook for just checking if alerts are enabled
 */
export function useAlertStatus() {
  const settingsContext = useSystemSettings();

  // ✅ Defensive check for undefined costSettings
  const costSettings = settingsContext?.costSettings || {
    trackUsage: false,
    budgetLimit: 100,
    alertThreshold: 80,
    showCostEstimates: false,
    preferCheaperModels: false
  };

  return {
    alertsEnabled: costSettings.trackUsage && costSettings.alertThreshold > 0,
    budgetLimit: costSettings.budgetLimit,
    alertThreshold: costSettings.alertThreshold
  };
}

/**
 * ✅ Hook for displaying alert notifications only
 */
export function useAlertNotifications() {
  const { toast } = useToast();
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize notification service
  useEffect(() => {
    if (!isInitialized) {
      notificationService.initialize(toast);
      setIsInitialized(true);
    }
  }, [toast, isInitialized]);

  // Subscribe to alerts for notifications only
  useEffect(() => {
    const unsubscribe = alertManager.onAlert((alert: ThresholdAlert) => {
      showAlertNotification(alert, {
        duration: alert.type === 'budget_exceeded' ? 12000 : 6000,
        showAction: true,
        actionText: 'Settings',
        onAction: () => {
          console.log('Navigate to cost settings');
        }
      });
    });

    return unsubscribe;
  }, []);

  return { isInitialized };
}

/**
 * ✅ Hook for manual alert testing
 */
export function useAlertTesting() {
  const settingsContext = useSystemSettings();

  // ✅ Defensive check for undefined costSettings
  const costSettings = settingsContext?.costSettings || {
    trackUsage: false,
    budgetLimit: 100,
    alertThreshold: 80,
    showCostEstimates: false,
    preferCheaperModels: false
  };

  const triggerTestAlert = useCallback((type: 'threshold' | 'budget') => {
    if (!costSettings.trackUsage) {
      console.warn('Alert testing: Cost tracking is disabled');
      return;
    }

    // Create a test alert
    const testAlert: ThresholdAlert = {
      id: `test_${type}_${Date.now()}`,
      type: type === 'threshold' ? 'threshold_exceeded' : 'budget_exceeded',
      message: type === 'threshold'
        ? `⚠️ Test Alert: You've exceeded your alert threshold`
        : `🚨 Test Alert: Budget exceeded`,
      currentCost: type === 'threshold' ? costSettings.budgetLimit * 0.85 : costSettings.budgetLimit * 1.1,
      threshold: type === 'threshold'
        ? (costSettings.budgetLimit * costSettings.alertThreshold) / 100
        : costSettings.budgetLimit,
      budgetLimit: costSettings.budgetLimit,
      utilizationPercentage: type === 'threshold' ? 85 : 110,
      timestamp: Date.now(),
      acknowledged: false,
      month: new Date().toISOString().slice(0, 7) // YYYY-MM
    };

    // Trigger the alert
    showAlertNotification(testAlert, {
      duration: 8000,
      showAction: true,
      actionText: 'Test Alert',
      onAction: () => console.log('Test alert action clicked')
    });

    console.log(`Alert testing: Triggered ${type} alert`);
    return testAlert;
  }, [costSettings]);

  return {
    triggerTestAlert,
    canTest: costSettings.trackUsage
  };
}
