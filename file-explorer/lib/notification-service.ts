// lib/notification-service.ts

import { ThresholdAlert } from './alert-manager';

/**
 * ✅ Notification Service
 * Handles displaying alerts in UI using toast notifications
 * Works in both Electron and Web environments
 */

export interface NotificationOptions {
  duration?: number;
  persistent?: boolean;
  showAction?: boolean;
  actionText?: string;
  onAction?: () => void;
}

export class NotificationService {
  private static instance: NotificationService | null = null;
  private toastFunction: ((options: any) => void) | null = null;
  private notificationQueue: Array<{ alert: ThresholdAlert; options?: NotificationOptions }> = [];
  private isProcessing = false;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * ✅ Initialize with toast function
   */
  public initialize(toastFunction: (options: any) => void): void {
    this.toastFunction = toastFunction;
    console.log('NotificationService: Initialized with toast function');
    
    // Process any queued notifications
    this.processQueue();
  }

  /**
   * ✅ Show threshold alert notification
   */
  public showThresholdAlert(alert: ThresholdAlert, options?: NotificationOptions): void {
    if (!this.toastFunction) {
      // Queue notification if toast function not available yet
      this.notificationQueue.push({ alert, options });
      console.warn('NotificationService: Toast function not available, queuing notification');
      return;
    }

    const toastOptions = this.createToastOptions(alert, options);
    
    try {
      this.toastFunction(toastOptions);
      console.log(`NotificationService: Displayed ${alert.type} notification`);
    } catch (error) {
      console.error('NotificationService: Failed to show toast:', error);
      
      // Fallback to browser notification if available
      this.showBrowserNotification(alert);
    }
  }

  /**
   * ✅ Show browser notification (fallback)
   */
  public showBrowserNotification(alert: ThresholdAlert): void {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      console.warn('NotificationService: Browser notifications not supported');
      return;
    }

    // Request permission if needed
    if (Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.createBrowserNotification(alert);
        }
      });
    } else if (Notification.permission === 'granted') {
      this.createBrowserNotification(alert);
    }
  }

  /**
   * ✅ Show Electron notification
   */
  public showElectronNotification(alert: ThresholdAlert): void {
    if (typeof window === 'undefined' || !window.electronAPI?.notification) {
      console.warn('NotificationService: Electron notifications not available');
      return;
    }

    try {
      window.electronAPI.notification.show({
        title: 'Budget Alert',
        body: alert.message,
        icon: alert.type === 'budget_exceeded' ? 'error' : 'warning',
        urgency: alert.type === 'budget_exceeded' ? 'critical' : 'normal'
      });
      console.log('NotificationService: Displayed Electron notification');
    } catch (error) {
      console.error('NotificationService: Failed to show Electron notification:', error);
    }
  }

  /**
   * ✅ Get notification message for alert type
   */
  public getNotificationMessage(alert: ThresholdAlert): string {
    switch (alert.type) {
      case 'threshold_exceeded':
        return `⚠️ Alert threshold exceeded: $${alert.currentCost.toFixed(2)} (${alert.utilizationPercentage.toFixed(1)}% of budget)`;
      
      case 'budget_exceeded':
        return `🚨 Budget exceeded: $${alert.currentCost.toFixed(2)} exceeds limit of $${alert.budgetLimit.toFixed(2)}`;
      
      case 'cost_warning':
        return `💰 Cost warning: $${alert.currentCost.toFixed(2)} approaching budget limit`;
      
      default:
        return alert.message;
    }
  }

  /**
   * ✅ Clear notification queue
   */
  public clearQueue(): void {
    this.notificationQueue = [];
    console.log('NotificationService: Notification queue cleared');
  }

  /**
   * ✅ Get queue size
   */
  public getQueueSize(): number {
    return this.notificationQueue.length;
  }

  // Private helper methods

  private createToastOptions(alert: ThresholdAlert, options?: NotificationOptions): any {
    const isError = alert.type === 'budget_exceeded';
    const defaultDuration = isError ? 10000 : 6000; // Longer for errors

    return {
      title: this.getToastTitle(alert),
      description: this.getNotificationMessage(alert),
      variant: isError ? 'destructive' : 'default',
      duration: options?.duration ?? defaultDuration,
      action: options?.showAction ? {
        altText: options.actionText || 'View Settings',
        onClick: options.onAction || (() => console.log('Open settings'))
      } : undefined
    };
  }

  private getToastTitle(alert: ThresholdAlert): string {
    switch (alert.type) {
      case 'threshold_exceeded':
        return 'Alert Threshold Exceeded';
      case 'budget_exceeded':
        return 'Budget Exceeded';
      case 'cost_warning':
        return 'Cost Warning';
      default:
        return 'Budget Alert';
    }
  }

  private createBrowserNotification(alert: ThresholdAlert): void {
    try {
      const notification = new Notification(this.getToastTitle(alert), {
        body: this.getNotificationMessage(alert),
        icon: '/favicon.ico',
        tag: `budget-alert-${alert.type}`, // Prevent duplicate notifications
        requireInteraction: alert.type === 'budget_exceeded'
      });

      // Auto-close after 10 seconds for non-critical alerts
      if (alert.type !== 'budget_exceeded') {
        setTimeout(() => notification.close(), 10000);
      }

      console.log('NotificationService: Displayed browser notification');
    } catch (error) {
      console.error('NotificationService: Failed to create browser notification:', error);
    }
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.notificationQueue.length === 0 || !this.toastFunction) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.notificationQueue.length > 0) {
        const { alert, options } = this.notificationQueue.shift()!;
        this.showThresholdAlert(alert, options);
        
        // Small delay between notifications to prevent spam
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } finally {
      this.isProcessing = false;
    }

    console.log('NotificationService: Processed notification queue');
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();

/**
 * ✅ Helper function to show alert notification
 */
export function showAlertNotification(
  alert: ThresholdAlert, 
  options?: NotificationOptions
): void {
  notificationService.showThresholdAlert(alert, options);
}

/**
 * ✅ Helper function to initialize notifications with toast
 */
export function initializeNotifications(toastFunction: (options: any) => void): void {
  notificationService.initialize(toastFunction);
}
