// lib/utils/use-debug.ts
"use client"

import { useEffect } from 'react';
import { useSystemSettings } from '../../components/settings/settings-context';
import { 
  setGlobalDebugMode, 
  debugLog, 
  debugLogCategory,
  debugLLM,
  debugAgent,
  debugConcurrency,
  debugFile,
  debugIPC,
  debugBoard,
  debugSettings,
  debugTiming,
  debugError,
  debugWarn,
  debugPerformance,
  debugState,
  debugObject,
  debugNetwork,
  debugIf,
  debugTrace,
  debugGroup,
  debugTable,
  debugMemory,
  debugExecutionTime,
  debugAsyncExecutionTime
} from './debug';

/**
 * ✅ React hook for debug mode operations
 * Automatically syncs with SystemSettings.debugMode
 */
export function useDebug() {
  let debugMode = false;
  
  try {
    const { systemSettings } = useSystemSettings();
    debugMode = systemSettings.debugMode || false;
  } catch (error) {
    // Fallback when settings context not available
    debugMode = false;
  }

  // Update global debug mode when settings change
  useEffect(() => {
    setGlobalDebugMode(debugMode);
    if (debugMode) {
      debugSettings('Debug mode enabled');
    }
  }, [debugMode]);

  return {
    debugMode,
    debugLog,
    debugLogCategory,
    debugLLM,
    debugAgent,
    debugConcurrency,
    debugFile,
    debugIPC,
    debugBoard,
    debugSettings,
    debugTiming,
    debugError,
    debugWarn,
    debugPerformance,
    debugState,
    debugObject,
    debugNetwork,
    debugIf,
    debugTrace,
    debugGroup,
    debugTable,
    debugMemory,
    debugExecutionTime,
    debugAsyncExecutionTime
  };
}

/**
 * ✅ Hook for component-specific debug logging
 */
export function useComponentDebug(componentName: string) {
  const debug = useDebug();

  const componentDebugLog = (...args: any[]) => {
    debug.debugLogCategory(componentName, ...args);
  };

  const componentDebugState = (oldState: any, newState: any, ...args: any[]) => {
    debug.debugState(componentName, oldState, newState, ...args);
  };

  const componentDebugError = (error: any, ...args: any[]) => {
    debug.debugError(error, componentName, ...args);
  };

  const componentDebugTiming = (label: string, startTime: number, ...args: any[]) => {
    debug.debugTiming(`${componentName}:${label}`, startTime, ...args);
  };

  return {
    ...debug,
    componentDebugLog,
    componentDebugState,
    componentDebugError,
    componentDebugTiming
  };
}

/**
 * ✅ Hook for agent-specific debug logging
 */
export function useAgentDebug(agentId: string) {
  const debug = useDebug();

  const agentDebugLog = (...args: any[]) => {
    debug.debugAgent(`[${agentId}]`, ...args);
  };

  const agentDebugTiming = (operation: string, startTime: number, ...args: any[]) => {
    debug.debugTiming(`Agent ${agentId} ${operation}`, startTime, ...args);
  };

  const agentDebugError = (error: any, operation?: string, ...args: any[]) => {
    debug.debugError(error, `Agent ${agentId}${operation ? `:${operation}` : ''}`, ...args);
  };

  const agentDebugPerformance = (metric: string, value: number, unit = 'ms', ...args: any[]) => {
    debug.debugPerformance(`Agent ${agentId} ${metric}`, value, unit, ...args);
  };

  return {
    ...debug,
    agentDebugLog,
    agentDebugTiming,
    agentDebugError,
    agentDebugPerformance
  };
}

/**
 * ✅ Hook for LLM-specific debug logging
 */
export function useLLMDebug(provider?: string, model?: string) {
  const debug = useDebug();

  const llmDebugLog = (...args: any[]) => {
    const prefix = provider && model ? `[${provider}/${model}]` : provider ? `[${provider}]` : '';
    debug.debugLLM(prefix, ...args);
  };

  const llmDebugTiming = (operation: string, startTime: number, ...args: any[]) => {
    const prefix = provider && model ? `${provider}/${model}` : provider || 'LLM';
    debug.debugTiming(`${prefix} ${operation}`, startTime, ...args);
  };

  const llmDebugNetwork = (method: string, url: string, status?: number, ...args: any[]) => {
    debug.debugNetwork(method, url, status, provider, model, ...args);
  };

  return {
    ...debug,
    llmDebugLog,
    llmDebugTiming,
    llmDebugNetwork
  };
}
